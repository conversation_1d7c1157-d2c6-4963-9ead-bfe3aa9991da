<script setup>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/authStore.js'

const authStore = useAuthStore()

// Локальное состояние формы
const nickname = ref('')

// Инициализация при Mount компонента
onMounted(async () => {
  await authStore.initialize()
})

// Обработчик отправки формы
async function handleSubmit() {
  if (!nickname.value.trim()) {
    return
  }

  await authStore.register(nickname.value.trim())
}

// Обработчик очистки ошибки
function handleClearError() {
  authStore.clearError()
}
</script>

<template>
  <div class="auth-container">
    <!-- Лодер -->
    <div v-if="authStore.isLoading" class="loader-container">
      <div class="loader"></div>
      <p class="loader-text">Загрузка...</p>
    </div>

    <!-- Форма регистрации -->
    <div v-else-if="!authStore.isAuthenticated && !authStore.isLoading" class="auth-form">
      <h1 class="auth-title">Geo Royale</h1>

      <!-- Ошибка -->
      <div v-if="authStore.error" class="error-container">
        <p class="error-text">{{ authStore.error }}</p>
        <button @click="handleClearError" class="error-close">×</button>
      </div>

      <!-- Форма -->
      <form @submit.prevent="handleSubmit" class="form">
        <div class="input-group">
          <label for="nickname" class="input-label">Никнейм</label>
          <input
            id="nickname"
            v-model="nickname"
            type="text"
            class="input-field"
            placeholder="Введите ваш никнейм"
            maxlength="20"
            required
            :disabled="authStore.isLoading"
          />
        </div>

        <button
          type="submit"
          class="submit-button"
          :disabled="authStore.isLoading || !nickname.trim()"
        >
          Войти
        </button>
      </form>
    </div>
  </div>
</template>

<style scoped>
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.loader-container {
  text-align: center;
  color: white;
}

.loader {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loader-text {
  font-size: 18px;
  margin: 0;
}

.auth-form {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 400px;
}

.auth-title {
  text-align: center;
  margin: 0 0 30px;
  color: #333;
  font-size: 28px;
  font-weight: bold;
}

.error-container {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.error-text {
  color: #c33;
  margin: 0;
  font-size: 14px;
}

.error-close {
  background: none;
  border: none;
  color: #c33;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-close:hover {
  background: #fcc;
  border-radius: 50%;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.input-field {
  padding: 12px 16px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.input-field:focus {
  outline: none;
  border-color: #667eea;
}

.input-field:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.submit-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 14px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
</style>
