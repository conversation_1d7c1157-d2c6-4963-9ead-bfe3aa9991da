// Сервис для работы с API авторизации

import { API_ENDPOINTS } from '@/constants/api.js'
import { post, get } from '@/services/httpService.js'

/**
 * Зарегистрировать нового пользователя
 * @param {string} nickname - Никнейм пользователя
 * @returns {Promise<string>} JWT токен
 */
export async function register(nickname) {
  if (!nickname || typeof nickname !== 'string' || nickname.trim().length === 0) {
    throw new Error('Никнейм не может быть пустым')
  }

  const trimmedNickname = nickname.trim()

  if (trimmedNickname.length < 2) {
    throw new Error('Никнейм должен содержать минимум 2 символа')
  }

  if (trimmedNickname.length > 20) {
    throw new Error('Никнейм не может быть длиннее 20 символов')
  }

  const response = await post(API_ENDPOINTS.AUTH_REGISTER, { nickname: trimmedNickname })

  if (!response.token || typeof response.token !== 'string') {
    throw new Error('Сервер вернул некорректный токен')
  }

  return response.token
}

/**
 * Получить профиль пользователя по JWT токену
 * @param {string} jwt - JWT токен
 * @returns {Promise<object>} Данные пользователя
 */
export async function getProfile(jwt) {
  if (!jwt || typeof jwt !== 'string') {
    throw new Error('JWT токен обязателен')
  }

  const response = await get(API_ENDPOINTS.AUTH_PROFILE, {
    headers: {
      'Authorization': `Bearer ${jwt}`,
    },
  })

  if (!response.id || !response.nickname) {
    throw new Error('Сервер вернул некорректные данные пользователя')
  }

  return {
    id: response.id,
    nickname: response.nickname,
  }
}
